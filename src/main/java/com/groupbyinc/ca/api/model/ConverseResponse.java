package com.groupbyinc.ca.api.model;

import io.micronaut.serde.annotation.Serdeable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Serdeable
public record ConverseResponse(
    @NotBlank String sessionId,
    @NotNull ResponseType responseType,
    @NotNull @Valid AssistantMessage assistantMessage,
    @Valid UiAction uiAction,
    ResponseData data
) {}