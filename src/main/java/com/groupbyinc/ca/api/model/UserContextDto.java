package com.groupbyinc.ca.api.model;

import io.micronaut.serde.annotation.Serdeable;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Serdeable
public class UserContextDto {
    private String visitorId;
    private UserPreferencesDto preferences;
    private List<PurchaseDto> purchaseHistory;

    @Data
    @Serdeable
    public static class PurchaseDto {
        private String productId;
        private String productName;
        private double price;
        private Instant purchasedAt;
    }

    @Data
    @Serdeable
    public static class UserPreferencesDto {
        private String name;
        private String surname;
        private String title;
        private Map<String, Object> metadata;

    }
}
