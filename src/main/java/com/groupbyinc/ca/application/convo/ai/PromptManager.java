package com.groupbyinc.ca.application.convo.ai;

import dev.langchain4j.model.input.PromptTemplate;
import io.micronaut.context.annotation.Context;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.nio.charset.StandardCharsets;

@Context
public class PromptManager {

    public PromptTemplate getPromptTemplate(String path) {
        try (var inputStream = PromptManager.class.getResourceAsStream(path)) {
            if (inputStream == null) {
                throw new IllegalArgumentException("Prompt file not found: " + path);
            }
            String templateString = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            return PromptTemplate.from(templateString);
        } catch (IOException e) {
            throw new UncheckedIOException("Failed to load prompt: " + path, e);
        }
    }
}
