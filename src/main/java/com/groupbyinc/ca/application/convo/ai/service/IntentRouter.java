package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.MemoryId;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

import java.util.List;

public interface IntentRouter {
    enum Route {
        PRODUCT_DISCOVERY,
        RETURN_REFUND,
        CUSTOMER_SERVICE,
        CART_MANAGEMENT,
        UNKNOWN
    }

    @SystemMessage("""
        You are a master router for a conversational commerce AI.
        Based on the user's message, identify the primary intent and any key entities like product names or brands.
        Respond with a JSON object matching the required format.
        """)
    Route route(@UserMessage String userMessage, @MemoryId String sessionId);
}
