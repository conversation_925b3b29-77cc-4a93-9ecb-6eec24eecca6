package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.MemoryId;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

/**
 * Conversational AI agent for product discovery and recommendations.
 * This agent maintains conversation memory to provide personalized,
 * context-aware product assistance.
 */
public interface ProductDiscoveryAgent {

    /**
     * Engages in conversational product discovery with the user.
     * Maintains conversation history to provide contextual responses.
     *
     * @param sessionId Unique session identifier for conversation memory
     * @param userMessage The user's message or query
     * @param userContext JSON string containing user context for personalization (can be empty)
     * @return Conversational response about products
     */
    String chat(
        @MemoryId String sessionId,
        @UserMessage String userMessage,
        @V("user_context") String userContext
    );
}
