package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

import java.util.List;

/**
 * AI service for generating search suggestions based on user input.
 * This service provides predictive search-as-you-type functionality.
 */
public interface SuggestService {

    /**
     * Generates 5 likely questions/suggestions based on user input.
     *
     * @param userInput The partial or complete user input
     * @return List of 5 suggested questions
     */
    @SystemMessage("""
        Using the text submitted by the user, please predict the question they are asking as it pertains to an online Shoeby store search.
        Respond with 5 likely questions that fit, with no other text and no bullets or numbering.
        """)
    List<String> generateSuggestions(@UserMessage String userInput);
}
