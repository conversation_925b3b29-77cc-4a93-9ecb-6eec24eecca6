package com.groupbyinc.ca.application.convo.ai.service;

import com.groupbyinc.ca.api.model.*;
import com.groupbyinc.ca.application.convo.ConversationService;

import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class OrchestrationService implements ConversationService {
    private final IntentRouter router;
    private final ProductDiscoveryService productDiscoveryService;
    private final SuggestService suggestService;
    private static final List<String> DEFAULT_SUGGESTIONS =
        List.of("Browse categories", "View popular items", "Search products", "New arrivals", "Sale items");

    @Override
    public SuggestResponse suggest(SuggestRequest suggestRequest) {
        log.info("Processing suggest request for session: {}", suggestRequest.sessionId());

        try {
            // Use SuggestService to generate search suggestions
            List<String> suggestions = suggestService.generateSuggestions(suggestRequest.query());

            if (suggestions == null || suggestions.isEmpty()) {
                // Fallback suggestions
                suggestions = DEFAULT_SUGGESTIONS;
            }

            return new SuggestResponse(suggestions);
        } catch (Exception e) {
            log.error("Error processing suggest request: {}", e.getMessage(), e);
            return new SuggestResponse(DEFAULT_SUGGESTIONS);
        }
    }

    @Override
    public ConverseResponse converse(ConverseRequest converseRequest) {
        log.info("Processing converse request for session: {}", converseRequest.sessionId());

        try {
            // Get the first message for now
            var userMessage = converseRequest.messages().getFirst().content();

            // Route the request to determine intent
            var route = router.route(userMessage, converseRequest.sessionId());
            log.debug("Routed message to: {}", route);

            return switch (route) {
                case PRODUCT_DISCOVERY -> handleProductDiscovery(converseRequest, userMessage);
                case UNKNOWN -> handleUnknownIntent(converseRequest, userMessage);
                default -> handleUnknownIntent(converseRequest, userMessage);
            };

        } catch (Exception e) {
            log.error("Error processing converse request: {}", e.getMessage(), e);
            return createErrorResponse(converseRequest.sessionId());
        }
    }

    private ConverseResponse handleProductDiscovery(ConverseRequest request, String userMessage) {
        log.debug("Handling product discovery for message: {}", userMessage);

        ProductDiscoveryService.SearchResult searchResult = productDiscoveryService.discoverProducts(userMessage);

        if (searchResult.hasResults()) {
            return new ConverseResponse(
                request.sessionId(),
                ResponseType.PRODUCT_LIST,
                new AssistantMessage(
                    "I found some products that might interest you based on your request.",
                    "Here are some options for you"
                ),
                null, // No UI action needed for now
                new ResponseData(
                    searchResult.searchResults().get(searchResult.extractedQueries().getFirst()), // First query results
                    null, // No recommendations for now
                    null // No redirect
                )
            );
        } else {
            return new ConverseResponse(
                request.sessionId(),
                ResponseType.ANSWER,
                new AssistantMessage(
                    "I couldn't find any products matching your request. Could you try describing what you're looking for in a different way?",
                    "No products found"
                ),
                null,
                new ResponseData(Map.of(), List.of(), null)
            );
        }
    }

    private ConverseResponse handleUnknownIntent(ConverseRequest request, String userMessage) {
        log.debug("Handling unknown intent for message: {}", userMessage);

        return new ConverseResponse(
            request.sessionId(),
            ResponseType.ANSWER,
            new AssistantMessage(
                "I'm not sure how to help with that. I can help you find products - just describe what you're looking for!",
                "I can help you find products"
            ),
            null,
            null);
    }

    private ConverseResponse createErrorResponse(String sessionId) {
        return new ConverseResponse(
            sessionId,
            ResponseType.ERROR,
            new AssistantMessage("I'm sorry, I encountered an error processing your request.", "Error occurred"),
            null,
            null);
    }
}
