<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ApplicationEnv" type="MicronautRunConfigurationType" factoryName="Micronaut">
    <envs>
      <env name="MICRONAUT_ENVIRONMENTS" value="local" />
      <env name="MONGODB_URI" value="mongodb+srv://write_gbiqa_lo:<EMAIL>/?proxyHost=localhost&amp;proxyPort=1080" />
      <env name="RETAIL_SEARCH_KEY" value="561ae418-5448-405f-813b-6d7149b38cfd" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.groupbyinc.ca.Application" />
    <module name="conversational-ai.main" />
    <option name="VM_PARAMETERS" value="-Dlogback.configurationFile=logback-local.xml" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="true" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        <ENTRY IS_ENABLED="true" PARSER="env" IS_EXECUTABLE="false" PATH=".run/.env" />
      </ENTRIES>
    </extension>
    <option name="alternativeJrePath" />
    <option name="alternativeJrePathEnabled" value="false" />
    <option name="mainClass" value="com.groupbyinc.ca.Application" />
    <option name="passParentEnvs" value="true" />
    <option name="programParameters" value="" />
    <option name="vmParameters" value="-Dlogback.configurationFile=logback-local.xml" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>